FROM ubuntu:22.04
RUN apt-get update && apt-get install -y curl
WORKDIR /runner
RUN curl -o actions-runner-linux-x64-2.328.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.328.0/actions-runner-linux-x64-2.328.0.tar.gz
RUN echo "01066fad3a2893e63e6ca880ae3a1fad5bf9329d60e77ee15f2b97c148c3cd4e  actions-runner-linux-x64-2.328.0.tar.gz" | shasum -a 256 -c
RUN tar xzf actions-runner-linux-x64-2.328.0.tar.gz
CMD ["./run.sh"]